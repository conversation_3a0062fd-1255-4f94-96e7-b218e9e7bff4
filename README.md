# 微信小程序数据采集工具

## ⚠️ 重要声明

**本工具仅用于合法的数据采集目的，使用前请确保：**

1. 已获得小程序官方授权
2. 已获得用户明确同意
3. 遵守相关法律法规
4. 符合微信平台规则

## 🚀 功能特性

- 合法的API调用方式
- 用户授权验证
- 数据本地存储
- 速率限制保护
- 详细日志记录

## 📋 使用前准备

### 1. 获取官方授权
- 注册微信小程序开发者账号
- 获取AppID和AppSecret
- 配置服务器域名白名单

### 2. 安装依赖
```bash
pip install requests
```

### 3. 配置文件
复制 `config_template.json` 为 `config.json` 并填入真实信息：

```json
{
  "app_id": "你的真实AppID",
  "app_secret": "你的真实AppSecret",
  ...
}
```

## 🔧 使用方法

### 基本使用
```bash
python wechat_data_collector.py
```

### 高级配置
编辑 `config.json` 文件调整采集参数：

- `rate_limit`: 请求间隔时间（秒）
- `output_dir`: 数据保存目录
- `max_requests_per_minute`: 每分钟最大请求数

## 📁 输出文件

采集的数据将保存在 `./data/` 目录下：
- `wechat_data_YYYYMMDD_HHMMSS.json`: 采集的数据文件
- `wechat_collector.log`: 运行日志

## 🔒 隐私保护

### 数据处理原则
1. **最小化原则**: 只采集必要的数据
2. **透明原则**: 明确告知用户数据用途
3. **安全原则**: 加密存储敏感数据
4. **时限原则**: 定期清理过期数据

### 用户权利
- 查看采集的数据
- 要求删除个人数据
- 撤回授权同意

## 📚 合法替代方案

### 1. 官方数据导出
- 使用小程序后台的数据导出功能
- 通过云开发控制台导出数据

### 2. 第三方统计工具
- 微信小程序数据助手
- 友盟统计
- 腾讯移动分析

### 3. 自建数据收集
- 在小程序内集成数据收集SDK
- 用户主动上传数据
- 通过表单收集反馈

## ⚖️ 法律风险提示

### 可能违法的行为
- 未经授权爬取他人小程序数据
- 绕过技术保护措施
- 采集用户隐私信息
- 商业化使用他人数据

### 建议的合规做法
- 获得明确的书面授权
- 制定详细的隐私政策
- 实施数据安全保护措施
- 定期进行合规审查

## 🛠️ 技术支持

如需技术支持或有疑问，请：
1. 查看微信官方开发文档
2. 咨询法律专业人士
3. 联系小程序平台客服

## 📄 许可证

本工具仅供学习和合法使用，使用者需自行承担法律责任。

---

**再次提醒：请确保您的使用行为完全合法合规！**
